import Pagination from '/@/components/Pagination/index.vue';
import RightToolbar from '/@/components/RightToolbar/index.vue';
import UploadExcel from '/@/components/Upload/Excel.vue';
import UploadFile from '/@/components/Upload/index.vue';
import UploadImg from '/@/components/Upload/Image.vue';
import Editor from '/@/components/Editor/index.vue';
import Tip from '/@/components/Tip/index.vue';
import SvgIcon from '/@/components/SvgIcon/index.vue';
import proForm from '/@/components/pro-form/index.vue';
import { useEnvStore } from '/@/stores/env';
// 第三方组件
import ElementPlus from 'element-plus';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import zhCn from 'element-plus/es/locale/lang/zh-cn'

import 'element-plus/dist/index.css';
import { Pane, Splitpanes } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
// 部门树组件
import vue3TreeOrg from 'vue3-tree-org';
import 'vue3-tree-org/lib/vue3-tree-org.css';

// 使用重构后的组件
import coTable from '@/refactor/packages/components/co-table/index.js';
import pageTable from '@/refactor/packages/components/page-table';
import { getDictionary } from '/@/api/common/dictionary.js'; // 获取字典值
import { formatDic } from '/@/utils/zszc.js';
import coForm from '@zszc/co-form-v3'
import '@zszc/co-form-v3/dist/style.css'
//预览
import coPreview from '@zszc/co-preview-v3';
import '@zszc/co-preview-v3/dist/index.css';

// 详情 - 返回
import proBackPre from '/@/components/pro-back-pre/index.vue';
import proFilelist from '/@/components/pro-filelist/index.vue';
import vEleDetail from '/@/components/pro-details/index.vue';
import proDetail from '@components/pro-detail/index.vue';

// @ts-ignore
import coUpload from '@zszc/co-upload-v3';
// 导入声明
import { App } from 'vue';
import request from '/@/utils/request';
import { getFiles } from '/@/api/common/upload.js';
const store = useEnvStore();
const env = computed(() => store.env);
export default {
	install(app: App) {
		app.component('proBackPre', proBackPre);
		app.component('proFilelist', proFilelist);
		app.component('vEleDetail', vEleDetail);
		app.component('proDetail', proDetail);
		app.component('Pagination', Pagination);
		app.component('RightToolbar', RightToolbar);
		app.component('uploadExcel', UploadExcel);
		app.component('UploadFile', UploadFile);
		app.component('UploadImg', UploadImg);
		app.component('Editor', Editor);
		app.component('Tip', Tip);
		app.component('SvgIcon', SvgIcon);
		app.component('proForm',proForm);
		app.component('pageTable',pageTable);

		// 导入全部的elmenet-plus的图标
		for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
			app.component(key, component);
			// 兼容性
			app.component(`ele-${key}`, component);
		}
		// 导入布局插件
		app.component('Splitpanes', Splitpanes);
		app.component('Pane', Pane);
		app.use(ElementPlus,{
			locale: zhCn,
		}); // ELEMENT 组件
		app.use(coForm,{
			fileSrc: function (v: string) {
				return getFiles([v]).then((res: any) => {
					const row = res.data[0];
					return { src: row.fileUrl, name: row.fileName };
				});
			},
		}); // ELEMENT 组件
		app.use(vue3TreeOrg); // 组织架构组件
		app.use(coPreview, {
			fileConfig: {
				prefix: '/ucenter',
			},
		}); // 预览组件

		//导入重构后的table组件
		app.use(coTable, {
			getDic: (params: any) => {
				// @ts-ignore
				return getDictionary(params).then(({ dicList }) => {
					return formatDic(dicList);
				});
			},
			attrs: {
				'header-cell-style': { backgroundColor: 'var(--el-table-row-hover-bg-color)', color: 'var(--el-text-color-primary)', align: 'left' },
				border: true,
			},
		});

		app.use(coUpload, {
			baseUrl: '/api',
			request,
		});
	},
};
