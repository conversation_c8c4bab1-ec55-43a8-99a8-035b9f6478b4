export default class Utils {
	/**
	 * 判断数据类型
	 * @param {any} value 数据
	 * @returns {string} 具体的类型 如：Array
	 */
	static getType(value) {
		const type = Object.prototype.toString.call(value);
		return {
			'[object Array]': 'Array',
			'[object Object]': 'Object',
			'[object String]': 'String',
			'[object Number]': 'Number',
			'[object Boolean]': 'Boolean',
			'[object Function]': 'Function',
			'[object AsyncFunction]': 'AsyncFunction',
			'[object Promise]': 'Promise',
		}[type];
	}
	/**
	 * 合并两个对象
	 * @param {object} obj1  源数据1
	 * @param {object} obj2  源数据2
	 * @returns 合并后的对象
	 */
	static deepMerge(obj1, obj2) {
		let key;
		for (key in obj2) {
			obj1[key] = obj1[key] && obj1[key].toString() === '[object Object]' ? this.deepMerge(obj1[key], obj2[key]) : (obj1[key] = obj2[key]);
		}
		return obj1;
	}
	static deepMerge2(...objects) {
		const result = {};
		for (const obj of objects) {
			for (const key in obj) {
				if (typeof obj[key] === 'object') {
					// 如果值是对象，则递归合并
					result[key] = this.deepMerge2(result[key], obj[key]);
				} else {
					// 否则直接赋值
					result[key] = obj[key];
				}
			}
		}
		return result;
	}
	/**
	 * 生成UUID
	 * @returns 生成的uuid
	 */
	static uuid = function () {
		return Math.floor(Math.random() * 100000 + Math.random() * 20000 + Math.random() * 5000);
	};
	/**
	 * 深度克隆
	 * @param {*} origin 源数据
	 * @returns {object} 返回克隆后的新数据
	 */
	static deepClone = function (origin) {
		if (typeof origin !== 'object' || origin == null) {
			return origin;
		}
		const result = Array.isArray(origin) ? [] : {};
		for (const key in origin) {
			if (Object.prototype.hasOwnProperty.call(origin, key)) {
				result[key] = this.deepClone(origin[key]);
			}
		}
		return result;
	};
	/**
	 * 创建a标签 下载文件
	 * @param {string} fileUrl 文件全路径
	 * @param {string} fileName 文件名称
	 */
	static downFile(fileUrl, fileName) {
		fetch(fileUrl)
			.then((res) => {
				return res.blob();
			})
			.then((blob) => {
				const link = document.createElement('a');
				link.href = URL.createObjectURL(blob);
				if (fileName) {
					link.download = fileName;
				} else {
					const tempArr = fileUrl.split('/');
					link.download = tempArr[tempArr.length - 1];
				}
				document.body.appendChild(link);
				link.click();
				document.body.removeChild(link);
			});
	}
	/**
	 *
	 * @param {object} 检测的数据对象
	 * @returns {boolean} 结果true/false
	 */
	static isEmpty(object) {
		return JSON.stringify(object) === '{}';
	}
	/**
	 * 格式化时间
	 * @param {date} time 目标值
	 * @param { string } pattern 需要的格式 如：yyyy-MM-dd
	 * @returns {string} 格式化后日期
	 */
	static parseTime(time, pattern) {
		if (arguments.length === 0 || !time) {
			return null;
		}
		const format = pattern || 'yyyy-MM-dd HH:mm:ss';
		let date;
		if (typeof time === 'object') {
			date = time;
		} else {
			if (typeof time === 'string' && /^[0-9]+$/.test(time)) {
				time = parseInt(time);
			} else if (typeof time === 'string') {
				time = time.replace(new RegExp(/-/gm), '/');
			}
			if (typeof time === 'number' && time.toString().length === 10) {
				time = time * 1000;
			}
			date = new Date(time);
		}
		const formatObj = {
			yyyy: date.getFullYear(),
			MM: date.getMonth() + 1,
			dd: date.getDate(),
			HH: date.getHours(),
			mm: date.getMinutes(),
			ss: date.getSeconds(),
			a: date.getDay(),
		};
		const time_str = format.replace(/(yyyy|MM|dd|HH|dd|mm|ss|ss|a)+/g, (result, key) => {
			let value = formatObj[key];
			// Note: getDay() returns 0 on Sunday
			if (key === 'a') {
				return ['日', '一', '二', '三', '四', '五', '六'][value];
			}
			if (result.length > 0 && value < 10) {
				value = '0' + value;
			}
			return value || 0;
		});
		return time_str;
	}
}
