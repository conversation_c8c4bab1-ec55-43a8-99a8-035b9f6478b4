<template>
	<el-form v-if="hasFormItem" class="zs-table-content" ref="tFormRef" :model="model" :size="configOpts.size" :validate-on-rule-change="false">
		<slot />
	</el-form>
	<template v-else>
		<slot />
	</template>
</template>

<script setup>
import { ref } from 'vue'

// 定义组件名称
defineOptions({
	name: 'CoContainer',
	inheritAttrs: false
})

// 定义props
const props = defineProps({
	model: Object,
	hasFormItem: Boolean,
	configOpts: Object,
})

// refs
const tFormRef = ref(null)

// 方法定义
const formRef = () => {
	return tFormRef.value
}

// 暴露给父组件的方法
defineExpose({
	formRef
})
</script>
