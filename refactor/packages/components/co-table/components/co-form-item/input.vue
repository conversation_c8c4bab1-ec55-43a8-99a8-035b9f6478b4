<template>
	<el-input v-model.trim="formModel[item.prop]" v-bind="itemAttrs" :placeholder="itemAttrs.placeholder || '请输入' + (itemAttrs.label || '')" v-on="listeners">
		<template v-for="slot in slotList" #[slot.name]>
			<template v-if="typeof item[slot.name] === 'string'">{{ item[slot.name] }}</template>
			<co-select v-else :key="slot.name" v-bind="{ item: slot[slot.name], dic: $attrs.dic, mainProp: slot[slot.name].prop ? '' : item.prop }" :row="formModel" :style="{ minWidth: slot[slot.name].width || '80px' }" @change="onPendChange" />
		</template>
	</el-input>
</template>

<script setup>
import { reactive, inject, useAttrs, getCurrentInstance } from 'vue'
import { handleFn } from './common.js'
import coSelect from './select.vue'

// 定义组件名称
defineOptions({
	name: 'CoInput',
	inheritAttrs: false
})

// 定义emits
const emit = defineEmits(['change'])

// 获取attrs和实例
const $attrs = useAttrs()
const instance = getCurrentInstance()

// 注入依赖
const widgetItem = inject('widgetItem')

// 响应式数据
const listeners = reactive({})
const { item, data = null, row = data, scene } = $attrs
const itemAttrs = reactive(Object.assign(item.attrs || {}, { clearable: true }))
const _inTable = scene === 'inTable'
const formModel = reactive(row)
const slotList = reactive([])

// 初始化
if (!formModel[item.prop]) {
	formModel[item.prop] = ''
}
widgetItem[item.prop] = instance

// 判断是否有 前置和后置
item.prepend &&
	slotList.push({
		name: 'prepend',
		prepend: item.prepend,
	})
item.append &&
	slotList.push({
		name: 'append',
		append: item.append,
	})

// 添加默认事件 change
listeners['change'] = (value) => handleFn.call(instance, 'change', value, _inTable, $attrs, itemAttrs)

// 为表格内表单时 追加其他事件
if (_inTable && item.events) {
	for (const evName of Object.keys(item.events)) {
		listeners[evName] = () => handleFn.call(instance, evName, row[item.prop], _inTable, $attrs, itemAttrs)
	}
}

// 方法定义
const resetField = (value) => {
	formModel[item.prop] = value
}

const onPendChange = (data) => {
	emit('change', { prop: data.prop, value: data.value })
}

// 暴露给父组件的方法
defineExpose({
	resetField
})
</script>
