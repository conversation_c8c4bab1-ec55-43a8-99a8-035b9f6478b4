<template>
	<el-upload
		class="upload-demo"
		v-bind="_attrs"
		action=""
		:multiple="false"
		list-type="text"
		:auto-upload="true"
		:limit="1"
		:drag="false"
		:show-file-list="false"
		:disabled="uploading||_attrs.disabled"
		:before-upload="file=>beforeUpload(file,_attrs)"
		:http-request="httpRequest"
	>
		<el-button v-if="typeof _attrs.styles==='string'" type="text" :[attrName]="_attrs.styles" :loading="uploading">{{ _attrs.text||'上传文件' }}</el-button>
	</el-upload>
</template>

<script setup>
import { ref, computed, onMounted, useAttrs, getCurrentInstance } from 'vue'
import defaultConfig from '../../config.js'
import Utils from '../../utils'

// 定义组件名称
defineOptions({
	name: 'CoUpload'
})

// 定义emits
const emit = defineEmits(['onSuccess'])

// 获取attrs和实例
const $attrs = useAttrs()
const instance = getCurrentInstance()
const $message = instance?.appContext.config.globalProperties.$message

// 响应式数据
const uploading = ref(false)
const attrName = ref('class')

// 计算属性
const _attrs = computed(() => {
	return (({ methodFn, linkProps, ...other }) => (other))($attrs)
})

// 方法定义
const httpRequest = (data) => {
	const uploadMethod = defaultConfig.upload
	const uploadFn = $attrs.methodFn || uploadMethod
	if (!uploadFn) {
		throw new Error(`upload: global upload and the custom upload at least one`)
	}
	if (!uploadFn || !Utils.getType(uploadFn) === 'Function') {
		throw new Error(`upload: parameter is wrong, should be function`)
	}
	uploadFn(data).then(res => {
		emit('onSuccess', res)
	}).finally(_ => {
		uploading.value = false
	})
}

const beforeUpload = (file, attrs) => {
	const extFileName = file.name.substring(file.name.lastIndexOf('.'))
	const uploadFileTypes = attrs.accept.split(',')
	if (uploadFileTypes.length > 0) {
		if (!uploadFileTypes.includes(extFileName)) {
			$message?.error('不支持的文件类型')
			return false
		}
	}
	const _maxSize = attrs.size || 10
	const fileSizeCheckResult = file.size / 1024 / 1024 <= _maxSize
	if (!fileSizeCheckResult) {
		$message?.error(`已超出文件大小，不能大于(${_maxSize}MB)`)
		return false
	}
	uploading.value = true
	return true
}

// 组件挂载
onMounted(() => {
	const attrsStyle = _attrs.value.styles
	attrName.value = attrsStyle ? typeof _attrs.value.styles === 'string' ? 'class' : 'style' : 'class'
})
</script>
