<script setup>
import Utils from "../utils"
import { h, useAttrs, getCurrentInstance } from "vue"

// 定义组件名称
defineOptions({
  name: "StaticComponent",
  inheritAttrs: false
})

// 获取attrs和实例
const $attrs = useAttrs()
const instance = getCurrentInstance()

const { item, row, column, handle, index } = $attrs

const renderByStyles = (styles, data) => {
  const rdData = data !== 0 && !data ? "-" : data
  return !styles || typeof styles === "string"
    ? h("span", { class: styles }, rdData)
    : h("span", { style: styles }, rdData)
}

const formatter =
  item.attrs && item.attrs.formatter ? item.attrs.formatter(row, column, row[item.prop], index) : null

const onHandle = (type) => handle({ type, field: item.prop, row, index })

// 根据类型 进行策略渲染模板
const typeTemplate = Object.freeze({
  date: () =>
    h(
      "span",
      {},
      formatter || row[item.prop] ? Utils.parseTime(row[item.prop], item.format || "yyyy-MM-dd HH:mm:ss") : "-"
    ),
  download: () =>
    h(
      "span",
      {
        style: "cursor:pointer",
        onClick: () => onHandle("download"),
      },
      renderByStyles(item.styles, formatter || item.text || "点击下载")
    ),
  preview: () =>
    h(
      "span",
      {
        style: "cursor:pointer",
        onClick: () => onHandle("preview"),
      },
      renderByStyles(item.styles, formatter || item.text || row[item.prop] || "点击查看")
    ),
  img: () =>
    row[item.prop] &&
    h(
      "img",
      {
        src: row[item.prop],
        style: { height: item.height },
        onClick: () => onHandle("preview"),
      },
      {
        placeholder: () => h("div", { class: "zs-flex-center" }, "加载中"),
      }
    ),
  enum: () => {
    const rowProp = row[item.prop]
    if (!rowProp) return h("span", null, "-")
    const itemColor = item.colors && item.colors[rowProp[item["valueKey"] || "value"]]
    const styles =
      itemColor && itemColor.indexOf("#") > -1 ? { style: { color: itemColor } } : { class: itemColor }
    return h("span", { ...styles }, rowProp[item["labelKey"] || "text"])
  },
  default: () => renderByStyles(item.styles || "", formatter || row[item.prop]),
})

// 渲染函数
instance.render = () => {
  return item.type && typeTemplate[item.type] ? typeTemplate[item.type]() : typeTemplate["default"]()
}
</script>
