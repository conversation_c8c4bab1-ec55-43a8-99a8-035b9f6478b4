<template>
	<el-button :class="item.classNew" v-bind="item" @click="onClick">
		<slot>{{ item.name }}</slot>
	</el-button>
</template>

<script setup>
import { ref, onMounted, useAttrs } from 'vue'

// 定义组件名称
defineOptions({
	name: 'CoButton',
	inheritAttrs: false
})

// 定义emits
const emit = defineEmits(['click'])

// 获取attrs
const $attrs = useAttrs()

// 响应式数据
const item = ref({})

// 方法定义
const onClick = (e) => {
	if ($attrs['dis-click']) return
	e.stopPropagation()
	emit('click')
}

// 组件挂载
onMounted(() => {
	const { item: itemData } = $attrs
	itemData['classNew'] = itemData['className']
	delete itemData['className']
	item.value = itemData
	itemData.tableKey && delete itemData.tableKey
	delete itemData.rule
	delete itemData.attributes
})
</script>
