<template>
  <co-search :model="formModel" :config="searchConfig" :dic="dicData" @search="onSearchHandle">
    <template v-for="(_, slotName) in slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-search>
  <co-table
    :id="props.id"
    :config="tableConfig"
    :header="tableHeader"
    @loaded="onTableLoaded"
    @dicLoaded="(data) => (dicData = data)"
    @operation="(data)=>emits('operation', data, onSearchHandle)"
    @selection-change="(selection) => emits('selection-change', selection)"
  >
    <template v-for="(_, slotName) in slots" :key="slotName" #[slotName]="slotProps">
      <slot :name="slotName" v-bind="slotProps || {}" />
    </template>
  </co-table>
</template>

<script setup>
import { ref, useSlots } from 'vue'

// 定义组件名称
defineOptions({
  name: "InnerTable"
})

// 定义emits
const emits = defineEmits(["operation", "selection-change", "search"])

// 获取slots
const slots = useSlots()

// 定义props
const props = defineProps({
  id: {
    type: String,
    default: ""
  },
  formModel: {
    type: Object,
    default: () => ({})
  },
  tableHeader: {
    type: Array,
    default: () => []
  },
  tableConfig: {
    type: Object,
    default: () => ({})
  },
  searchConfig: {
    type: Object,
    default: () => ({})
  }
})

// 响应式数据
let onSearch = null
const dicData = ref()
let oldParams = ref({})

// 方法定义
// 表格挂载完成
const onTableLoaded = ({ getDataList }) => (onSearch = getDataList)

const onSearchHandle = (params, type) => {
  if (params) oldParams.value = params
  onSearch && onSearch({ params: oldParams.value })
  emits("search", { params: oldParams.value, type })
}

// 暴露给父组件的方法
defineExpose({ 
  onSearchHandle, 
  oldParams 
})
</script>

<style lang="scss"></style>
